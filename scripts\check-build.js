#!/usr/bin/env node
/**
 * Check if the project is built and provide helpful instructions
 */

const fs = require('fs');
const path = require('path');

const distDir = path.join(__dirname, '..', 'dist');
const cliFile = path.join(distDir, 'cli', 'update-confluence-page-cli.js');

if (!fs.existsSync(distDir) || !fs.existsSync(cliFile)) {
    console.log('🔨 Build required!');
    console.log('==================');
    console.log('The TypeScript files need to be compiled first.');
    console.log('');
    console.log('Please run:');
    console.log('  npm run build');
    console.log('');
    console.log('Then try your command again.');
    console.log('');
    process.exit(1);
}

console.log('✅ Build check passed - project is ready to use!');
